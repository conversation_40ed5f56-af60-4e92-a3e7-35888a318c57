import time
import gc
from typing import Optional, Dict, List, Tuple, Union
import numpy as np
import pickle as pkl
import tracemalloc
import os
from dotenv import load_dotenv

import logging

load_dotenv()

BASE_DIRECTORY = os.getenv("BASE_DIRECTORY")
tracemalloc.start()


def log_memory(comment: str = None) -> None:
    size, peak = tracemalloc.get_traced_memory()
    mb_scale = 1024**2

    comment = comment.strip() + ": " if comment else ""
    tracemalloc.reset_peak()

    print(
        f"{comment}current_usage: {size / mb_scale:.4f} MB;",
        f"peak_usage: {peak / mb_scale:.4f} MB",
    )

    return peak / mb_scale


def load_single_file(file_path: str) -> np.ndarray:
    """
    Load a single pickle file and return the matrix and its size.
    Returns tuple of (matrix, size) or None if loading fails.
    """
    try:
        with open(file_path, "rb") as f:
            matrix = pkl.load(f)
            if np.any(np.sum(matrix, axis=1)) == 0:
                logging.warning(
                    f"The score matrix {file_path} has one or more matrices 0"
                )
            return matrix
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return np.array([[]]).reshape(-1, 1)


def get_dict(qid, result_ids, weighted_scores, result_orders, raw_scores, top_k):
    mask = result_ids != qid
    rid_list = result_ids[mask][:top_k].tolist()
    wscores_list = weighted_scores[mask][:top_k].tolist()
    rscores_list = raw_scores[result_orders[mask][:top_k]].tolist()
    del mask
    return dict(
        top_product_ids=rid_list,
        similarity_scores=wscores_list,
        score_by_attribute_by_result=rscores_list,
    )


class InferenceProductSearch:

    def __init__(
        self,
        database: Optional[str] = None,
    ) -> None:
        """Initialise the inference for product lineup

        Args:
            database (PostgresEngine): Database object.
            embeddings (Type[BaseEmbeddingsToolkit]): Embeddings toolkit.
            vdb_type (Literal['faiss', 'pgvector'], optional): Type of vector database used. Defaults to 'faiss'.
        """
        import json

        if database is None:
            with open(os.path.join(BASE_DIRECTORY, "info.json"), "r") as f:
                info = json.load(f)
            database = info.get("database")
        self._database_directory = os.path.join(BASE_DIRECTORY, database)
        self.load_metadata()

    def load_metadata(self):
        import json

        with open(
            os.path.join(self._database_directory, "attribute_ids_size.json"), "r"
        ) as f:
            self._attribute_ids_size = json.load(f)

        with open(
            os.path.join(self._database_directory, "attribute_ids_by_pcat.json"), "r"
        ) as f:
            self._attribute_ids_by_pcat = json.load(f)

        self._product_mapping_dir = os.path.join(
            self._database_directory, "product_mapping"
        )
        self._score_matrix_dir = os.path.join(self._database_directory, "score_matrix")

    def _get_attribute_ids(self, product_category_id: str):  # This can be cached.
        attribute_ids = self._attribute_ids_by_pcat[product_category_id]
        return attribute_ids

    # This should not work this way. You don't need to get all the matrices for the product category.
    # Instead it should take an ordered list of attribute IDs after filtering by weights.
    def _get_attribute_score_matrix(
        self, attribute_ids: np.ndarray, attribute_offsets: Optional[np.ndarray] = None
    ) -> np.ndarray[int]:
        """Get the attribute score matrix for a category id
        Args:
            product_category_id (int): The category id associated with a product.
        Returns:
            np.ndarray[int]: A matrix with the score for every attribute in the category
        """
        import os
        from threading import Thread

        start_time = time.time()
        print(f"Starting to load {len(attribute_ids)} matrices")
        file_paths = [
            os.path.join(self._score_matrix_dir, f"{attribute_id}.pkl")
            for attribute_id in attribute_ids
        ]

        num_files = attribute_ids.shape[0]
        batch_size = min(os.cpu_count(), len(file_paths))
        num_batch = (
            num_files // batch_size
            if (num_files // batch_size) == (num_files / batch_size)
            else (num_files // batch_size) + 1
        )
        batches = [
            (b * batch_size, min((b + 1) * batch_size, num_files))
            for b in range(num_batch)
        ]
        raw_matrices = []

        def load_file(file_dir, outputs, i):
            outputs[i] = load_single_file(file_dir)

        for b0, b1 in batches:
            chunk_paths = file_paths[b0:b1]
            num_workers = b1 - b0

            chunk_results = [None] * num_workers
            trds = [
                Thread(target=load_file, args=(fd, chunk_results, i))
                for i, fd in enumerate(chunk_paths)
            ]
            [trd.start() for trd in trds]
            [trd.join() for trd in trds]

            raw_matrices.extend(chunk_results)
        # not creating views of resized score matrices here yet
        sizes = (
            [sm.shape[0] - off for sm, off in zip(raw_matrices, attribute_offsets)]
            if attribute_offsets is not None
            else [sm.shape[0] for sm in raw_matrices]
        )
        max_size = int(np.max(sizes))
        padded_matrices = np.zeros(
            (num_files, max_size + 1, max_size + 1), dtype=np.float32
        )

        # shrink all the score matrices with attribute offsets
        for i, (matrix, offset, size) in enumerate(
            zip(raw_matrices, attribute_offsets, sizes)
        ):
            padded_matrices[i, :size, :size] = np.copy(
                matrix[offset:, offset:]
            )  # Materialise the slices directly during assignment

        # the original loaded matrices should not be referenced now, so this should clean up the RAM space
        del raw_matrices
        gc.collect()

        print(f"Processing completed in {time.time() - start_time:.2f} seconds")

        return padded_matrices

    def _get_weight_arr(
        self, weights: Optional[Dict[str, float]], product_category_id: int
    ) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        ao = self._get_attribute_ids(str(product_category_id))
        if weights is not None:
            weight_arr = np.array([weights.get(aid, 0.0) for aid in ao])
            weight_arr = weight_arr / weight_arr.sum()  # Normalise weight
            attr_indices = np.nonzero(weight_arr)[
                0
            ]  # Get the index of the non-zero attributes, this will be used to shrink sizes of all the other matrices
            weight_arr = weight_arr[attr_indices]
        else:
            weight_arr = np.ones((len(ao)))
            weight_arr = weight_arr / weight_arr.sum()
            attr_indices = np.arange(len(ao))
        ao = np.array(ao)[attr_indices]
        return weight_arr, attr_indices, ao

    def _get_records(
        self,
        product_category_id: int,
        product_id_list: Optional[List[int]] = None,
        used_indices: Optional[np.ndarray] = None,
    ) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Getting records having the same category id as the product query
        Args:
            product_category_id (int): The category id associated with a product.
        Returns:
            pd.DataFrame: A dataframe with products
        """
        import pickle

        if product_id_list:
            with open(
                f"{os.path.join(self._product_mapping_dir, str(product_category_id))}.pkl",
                "rb",
            ) as f:
                raw_term_indices, product_ids = pickle.load(f)
            indices = np.where(product_ids[:, None] == product_id_list)[0]
            product_ids = product_ids[indices]
            raw_term_indices = raw_term_indices[indices]
        else:
            with open(
                f"{os.path.join(self._product_mapping_dir, str(product_category_id))}.pkl",
                "rb",
            ) as f:
                raw_term_indices, product_ids = pickle.load(f)

        # Shrink the matrix here instead
        if used_indices is not None:
            term_indices = np.copy(raw_term_indices[:, used_indices])
        else:
            term_indices = raw_term_indices

        # Get the minimum indices for each attribute in the entire record dataset and used that as an offset for the records and score matrices later
        attribute_offsets = np.min(term_indices, axis=0)
        final_term_indices = term_indices - attribute_offsets
        del raw_term_indices, term_indices
        gc.collect()
        return product_ids, final_term_indices, attribute_offsets

    def _get_score_matrix_array(
        self, weights, product_category_id, product_ids=None, scope_product_id=None
    ):
        weights, used_indices, attribute_ids = self._get_weight_arr(
            weights=weights, product_category_id=product_category_id
        )
        list_to_search = (
            list(set(product_ids + scope_product_id))
            if scope_product_id is not None
            else scope_product_id
        )

        ref_product_ids, term_indices, attribute_offsets = self._get_records(
            product_category_id=product_category_id,
            product_id_list=list_to_search,
            used_indices=used_indices,
        )
        sm_arr = self._get_attribute_score_matrix(
            attribute_ids=attribute_ids, attribute_offsets=attribute_offsets
        )
        del attribute_offsets, list_to_search, used_indices, scope_product_id

        return sm_arr, ref_product_ids, term_indices, attribute_ids, weights

    def _get_spec_terms_indices_product_id(
        self,
        product_ids: list[int],
        product_category_id: int,
        weights: Optional[dict] = None,
        top_k: int = 5,
        scope_product_id: Optional[List[int]] = None,
    ) -> Dict:
        """Getting the scope products and their score using a list of product ids with batching."""
        log_memory("Start of the script")
        batch_size = 10
        # Initial setup
        sm_arr, ref_product_ids, term_indices, attribute_ids, weights = (
            self._get_score_matrix_array(
                weights=weights,
                product_category_id=product_category_id,
                product_ids=product_ids,
                scope_product_id=scope_product_id,
            )
        )
        scope_ids = np.array(scope_product_id) if scope_product_id else ref_product_ids

        log_memory("After loading matrices")

        # Convert to numpy arrays and delete original lists
        query_ids = np.array(product_ids)
        del product_ids

        num_queries = len(query_ids)
        num_attributes = sm_arr.shape[0]

        result_dicts = {}

        # restructure the for loop to be more generic
        for batch_start in range(0, num_queries, batch_size):
            batch_end = min(batch_start + batch_size, num_queries)
            batch_query_ids = query_ids[batch_start:batch_end]

            # Get indices and immediately delete temporary arrays
            batch_query_indices = np.where(ref_product_ids == batch_query_ids[:, None])[
                1
            ]
            scope_indices = (
                np.where(scope_ids == ref_product_ids[:, None])[1]
                if scope_product_id
                else np.arange(ref_product_ids.shape[0])
            )
            batch_query_arr = term_indices[batch_query_indices, :]
            sizes = np.array([self._attribute_ids_size[str(i)] for i in attribute_ids])
            scope_arr = term_indices[scope_indices, :]
            del batch_query_indices, scope_indices

            q_indices = batch_query_arr[:, None, :].astype(dtype="int32")

            mask = (q_indices != sizes).astype(int)
            query_total_weight = (mask * weights).sum(axis=-1)
            sc_indices = scope_arr[None, :, :].astype(dtype="int32")
            del batch_query_arr, scope_arr

            raw_scores = sm_arr[
                np.arange(num_attributes, dtype="int")[None, None, :],
                q_indices,
                sc_indices,
            ]
            del q_indices, sc_indices
            log_memory("After Getting the raw scores")
            weighted_scores = raw_scores * weights
            record_sums = weighted_scores.sum(axis=2)
            raw_weighted = record_sums / query_total_weight
            raw_weighted = np.nan_to_num(raw_weighted)
            log_memory("After dot")

            result_orders = np.argsort(raw_weighted, axis=-1)[:, ::-1]

            ordered_scores = np.take_along_axis(raw_weighted, result_orders, axis=1)[
                :, : top_k + 1
            ]
            ordered_results = np.take_along_axis(
                scope_ids[None, :], result_orders, axis=1
            )[:, : top_k + 1]
            result_orders = result_orders[:, : top_k + 1]
            del raw_weighted

            batch_dicts = [
                get_dict(qid, rids, wscores, ro, rscores, top_k)
                for qid, rids, wscores, ro, rscores in zip(
                    batch_query_ids,
                    ordered_results,
                    ordered_scores,
                    result_orders,
                    raw_scores,
                )
            ]
            del ordered_scores, ordered_results, result_orders, raw_scores

            result_dicts.update(dict(zip(batch_query_ids.tolist(), batch_dicts)))
            del batch_dicts, batch_query_ids

            gc.collect()
            log_memory(f"Processed batch {batch_start//batch_size + 1}")

        # Create output dictionary and delete remaining large objects
        output_dict = dict(
            results=result_dicts,
            metadata=dict(
                attribute_id_order=attribute_ids.tolist(),
                product_category_id=product_category_id,
                weight=weights.tolist(),
            ),
        )

        # Final cleanup of all remaining large objects
        del (
            ref_product_ids,
            term_indices,
            weights,
            attribute_ids,
            sm_arr,
            query_ids,
            scope_ids,
            result_dicts,
        )
        gc.collect()
        log_memory("After finishing indexing")

        return output_dict

    def _get_spec_term_indices_attribute(
        self,
        specs: list[dict[str, str]],
        product_category_id: int,
        weights: Optional[dict] = None,
        top_k: int = 5,
        scope_product_id: Optional[list[int]] = None,
    ):
        """Getting the scope products and the score using a list of product ids.

        Args:
            specs (list[dict[str, str]]): Specs of the query to get the terms for similarity search
            weights (Optional[dict]): Weighting for the attributes. Defaults to None.
        Returns:
            Dict[int, List[Tuple[int, float, np.ndarray[float]]]]: Dictionary with keys being the product IDs of the search queries and values (product IDs) and their respective scores
        """
        """Getting the scope products and their score using a list of product ids with batching."""
        log_memory("Start of the script")

        # Initial setup
        start_weight = time.time()
        weights, used_indices, attribute_ids = self._get_weight_arr(
            weights=weights, product_category_id=product_category_id
        )
        log_memory("After weight arr")
        print("Get Weight arr TIME: ", time.time() - start_weight)

        # Create list_to_search and immediately delete input if possible
        list_to_search = scope_product_id
        if scope_product_id is not None:
            del scope_product_id

        start_get_records = time.time()
        ref_product_ids, term_indices, attribute_offsets = self._get_records(
            product_category_id=product_category_id,
            product_id_list=list_to_search,
            used_indices=used_indices,
        )
        del list_to_search, used_indices
        log_memory("After getting records")
        print("Get Records TIME:", time.time() - start_get_records)

        start_score_matrix = time.time()
        sm_arr = self._get_attribute_score_matrix(
            attribute_ids=attribute_ids, attribute_offsets=attribute_offsets
        )
        del attribute_offsets
        log_memory("After loading matrices")
        print("Get Score Matrix TIME:", time.time() - start_score_matrix)

        print("Start q-key indexing with batching")
        scope_ids = np.array(scope_product_id) if scope_product_id else ref_product_ids
        default_arr = np.vectorize(lambda x: self._attribute_ids_size[str(x)])(
            attribute_ids
        )[None, :]
        num_queries = len(specs)
        query_arr = np.concatenate([default_arr] * num_queries, axis=0)
        for i, q in enumerate(specs):
            q_key = np.array(list(q.keys()))
            q_val = np.array(list(q.values()))
            q_key_indices = np.where(attribute_ids == q_key[:, None])[1]
            query_arr[i][q_key_indices] = q_val

        num_attributes = sm_arr.shape[0]

        def get_dict(qid, result_ids, weighted_scores, result_orders, raw_scores):
            mask = result_ids != qid
            rid_list = result_ids[mask][:top_k].tolist()
            wscores_list = weighted_scores[mask][:top_k].tolist()
            rscores_list = raw_scores[result_orders[mask][:top_k]].tolist()
            del mask
            return dict(
                top_product_ids=rid_list,
                similarity_scores=wscores_list,
                score_by_attribute_by_result=rscores_list,
            )

        scope_indices = (
            np.where(scope_ids == ref_product_ids[:, None])[1]
            if scope_product_id
            else np.arange(ref_product_ids.shape[0])
        )
        scope_arr = term_indices[scope_indices, :]
        sc_indices = scope_arr[None, :, :].astype(dtype="int32")
        q_indices = query_arr[:, None, :].astype(dtype="int32")

        sizes = np.array([self._attribute_ids_size[str(i)] for i in attribute_ids])
        mask = (q_indices != sizes).astype(int)
        query_total_weight = (mask * weights).sum(axis=-1)

        raw_scores = sm_arr[
            np.arange(num_attributes, dtype="int")[None, None, :], q_indices, sc_indices
        ]

        weighted_scores = raw_scores * weights
        record_sums = weighted_scores.sum(axis=2)
        raw_weighted = record_sums / query_total_weight
        result_orders = np.argsort(raw_weighted, axis=-1)[:, ::-1]

        ordered_scores = np.take_along_axis(raw_weighted, result_orders, axis=1)[
            :, : top_k + 1
        ]
        ordered_results = np.take_along_axis(scope_ids[None, :], result_orders, axis=1)[
            :, : top_k + 1
        ]
        result_orders = result_orders[:, : top_k + 1]
        result_dicts = [
            get_dict(qid, rids, wscores, ro, rscores)
            for qid, rids, wscores, ro, rscores in zip(
                ref_product_ids,
                ordered_results,
                ordered_scores,
                result_orders,
                raw_scores,
            )
        ]
        del raw_weighted
        return result_dicts

    def search_with_exist_products(
        self,
        query_product_ids: Union[int, List[int]],
        product_category_id: int,
        weights: Optional[dict] = None,
        top_k: int = 5,
        scope_product_id: Optional[list[int]] = None,
    ) -> Dict[int, List[Tuple[int, float, np.ndarray[float]]]]:
        """Search for similar products with an existing product

        Args:
            query_product_ids (Union[int, List[int]]): Single product ID or list of product IDs as queries.
            weights (Optional[Dict[int, float]], optional): Weighting for the attributes. Defaults to None.
            top_k (int, optional): Number of results to show for each query. Defaults to 5.
            scope_sql_query (Optional[str]):
        Returns:
            Dict[int, List[Tuple[int, float, np.ndarray[float]]]]: Dictionary with keys being the product IDs of the search queries and values being list of top k results (product IDs) and their respective scores.
        """
        products = self._get_spec_terms_indices_product_id(
            product_ids=query_product_ids,
            product_category_id=product_category_id,
            weights=weights,
            top_k=top_k,
            scope_product_id=scope_product_id,
        )
        return products

    def search_with_specs(
        self,
        specs: list[dict[str, str]],
        product_category_id: int,
        weights: Optional[dict] = None,
        top_k: int = 5,
        scope_product_id: Optional[str] = None,
    ) -> Dict[int, List[Tuple[int, float, np.ndarray[float]]]]:
        """Search for similar products given a list of specifications

        Args:
            specs (list[dict[str, str]]): Specs of the query to get the terms for similarity search
            weights (Optional[dict]): Weighting for the attributes. Defaults to None.
            top_k (int, optional): Number of results to show for each query. Defaults to 5.
            scope_sql_query (Optional[str]):

        Returns:
            Dict[int, List[Tuple[int, float, np.ndarray[float]]]]: Dictionary with keys being the product IDs of the search queries and values being list of top k results (product IDs) and their respective scores
        """
        products = self._get_spec_term_indices_attribute(
            specs=specs,
            product_category_id=product_category_id,
            weights=weights,
            top_k=top_k,
            scope_product_id=scope_product_id,
        )
        return products
