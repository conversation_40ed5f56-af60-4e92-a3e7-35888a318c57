"""
Debug utilities for investigating inference errors.

This module provides tools to help diagnose issues with the inference engine,
particularly around missing categories, corrupted metadata, and data inconsistencies.
"""

import os
import json
import pickle
from typing import Dict, List, Optional, Tuple
from app.utils.logging import setup_logging

logger = setup_logging(component_name="debug_inference")


class InferenceDebugger:
    """Debug utility for investigating inference engine issues."""
    
    def __init__(self, database_path: str):
        """
        Initialize the debugger with a database path.
        
        Args:
            database_path: Path to the database directory
        """
        self.database_path = database_path
        self.metadata_loaded = False
        
    def load_metadata(self) -> Dict:
        """
        Load and validate all metadata files.
        
        Returns:
            Dictionary containing metadata status and content
        """
        result = {
            "database_path": self.database_path,
            "files_status": {},
            "metadata": {},
            "errors": [],
            "warnings": []
        }
        
        # Check attribute_ids_size.json
        size_file = os.path.join(self.database_path, "attribute_ids_size.json")
        try:
            with open(size_file, "r") as f:
                attribute_ids_size = json.load(f)
            result["files_status"]["attribute_ids_size.json"] = "OK"
            result["metadata"]["attribute_ids_size"] = attribute_ids_size
            logger.info(f"Loaded attribute_ids_size.json with {len(attribute_ids_size)} entries")
        except FileNotFoundError:
            error = f"Missing file: {size_file}"
            result["errors"].append(error)
            result["files_status"]["attribute_ids_size.json"] = "MISSING"
            logger.error(error)
        except json.JSONDecodeError as e:
            error = f"Corrupted file: {size_file} - {str(e)}"
            result["errors"].append(error)
            result["files_status"]["attribute_ids_size.json"] = "CORRUPTED"
            logger.error(error)
        
        # Check attribute_ids_by_pcat.json
        pcat_file = os.path.join(self.database_path, "attribute_ids_by_pcat.json")
        try:
            with open(pcat_file, "r") as f:
                attribute_ids_by_pcat = json.load(f)
            result["files_status"]["attribute_ids_by_pcat.json"] = "OK"
            result["metadata"]["attribute_ids_by_pcat"] = attribute_ids_by_pcat
            logger.info(f"Loaded attribute_ids_by_pcat.json with {len(attribute_ids_by_pcat)} categories")
        except FileNotFoundError:
            error = f"Missing file: {pcat_file}"
            result["errors"].append(error)
            result["files_status"]["attribute_ids_by_pcat.json"] = "MISSING"
            logger.error(error)
        except json.JSONDecodeError as e:
            error = f"Corrupted file: {pcat_file} - {str(e)}"
            result["errors"].append(error)
            result["files_status"]["attribute_ids_by_pcat.json"] = "CORRUPTED"
            logger.error(error)
        
        # Check directories
        product_mapping_dir = os.path.join(self.database_path, "product_mapping")
        score_matrix_dir = os.path.join(self.database_path, "score_matrix")
        
        if os.path.exists(product_mapping_dir):
            result["files_status"]["product_mapping_dir"] = "OK"
            mapping_files = [f for f in os.listdir(product_mapping_dir) if f.endswith('.pkl')]
            result["metadata"]["product_mapping_files"] = len(mapping_files)
            logger.info(f"Found {len(mapping_files)} product mapping files")
        else:
            error = f"Missing directory: {product_mapping_dir}"
            result["errors"].append(error)
            result["files_status"]["product_mapping_dir"] = "MISSING"
            logger.error(error)
        
        if os.path.exists(score_matrix_dir):
            result["files_status"]["score_matrix_dir"] = "OK"
            matrix_files = [f for f in os.listdir(score_matrix_dir) if f.endswith('.pkl')]
            result["metadata"]["score_matrix_files"] = len(matrix_files)
            logger.info(f"Found {len(matrix_files)} score matrix files")
        else:
            error = f"Missing directory: {score_matrix_dir}"
            result["errors"].append(error)
            result["files_status"]["score_matrix_dir"] = "MISSING"
            logger.error(error)
        
        self.metadata_loaded = True
        return result
    
    def investigate_category_error(self, category_id: int, product_id: Optional[int] = None) -> Dict:
        """
        Investigate a specific category error.
        
        Args:
            category_id: The category ID that's causing issues
            product_id: Optional product ID for additional context
            
        Returns:
            Dictionary containing investigation results
        """
        result = {
            "category_id": category_id,
            "product_id": product_id,
            "investigation": {},
            "recommendations": []
        }
        
        # Load metadata if not already loaded
        if not self.metadata_loaded:
            metadata_result = self.load_metadata()
            if metadata_result["errors"]:
                result["investigation"]["metadata_errors"] = metadata_result["errors"]
                result["recommendations"].append("Fix metadata file issues first")
                return result
        
        # Check if category exists in attribute_ids_by_pcat
        pcat_file = os.path.join(self.database_path, "attribute_ids_by_pcat.json")
        try:
            with open(pcat_file, "r") as f:
                attribute_ids_by_pcat = json.load(f)
            
            category_str = str(category_id)
            if category_str in attribute_ids_by_pcat:
                result["investigation"]["category_in_metadata"] = True
                result["investigation"]["attribute_count"] = len(attribute_ids_by_pcat[category_str])
                logger.info(f"Category {category_id} found in metadata with {len(attribute_ids_by_pcat[category_str])} attributes")
            else:
                result["investigation"]["category_in_metadata"] = False
                available_categories = sorted([int(k) for k in attribute_ids_by_pcat.keys()])
                result["investigation"]["available_categories"] = available_categories[:20]  # First 20
                result["investigation"]["total_categories"] = len(available_categories)
                result["recommendations"].append(f"Category {category_id} not found in preprocessing. Re-run preprocessing to include this category.")
                logger.warning(f"Category {category_id} not found. Available: {available_categories[:10]}...")
        except Exception as e:
            result["investigation"]["metadata_load_error"] = str(e)
            result["recommendations"].append("Fix metadata file corruption")
        
        # Check if product mapping file exists
        mapping_file = os.path.join(self.database_path, "product_mapping", f"{category_id}.pkl")
        if os.path.exists(mapping_file):
            result["investigation"]["mapping_file_exists"] = True
            try:
                with open(mapping_file, "rb") as f:
                    raw_term_indices, product_ids = pickle.load(f)
                result["investigation"]["products_in_mapping"] = len(product_ids)
                if product_id and product_id in product_ids:
                    result["investigation"]["product_in_mapping"] = True
                elif product_id:
                    result["investigation"]["product_in_mapping"] = False
                    result["recommendations"].append(f"Product {product_id} not found in category {category_id} mapping")
                logger.info(f"Mapping file for category {category_id} contains {len(product_ids)} products")
            except Exception as e:
                result["investigation"]["mapping_file_error"] = str(e)
                result["recommendations"].append("Mapping file is corrupted, re-run preprocessing")
        else:
            result["investigation"]["mapping_file_exists"] = False
            result["recommendations"].append(f"Product mapping file missing for category {category_id}")
        
        return result
    
    def validate_database_consistency(self) -> Dict:
        """
        Validate consistency across all database files.
        
        Returns:
            Dictionary containing validation results
        """
        result = {
            "validation_status": "UNKNOWN",
            "issues": [],
            "statistics": {}
        }
        
        metadata_result = self.load_metadata()
        if metadata_result["errors"]:
            result["validation_status"] = "FAILED"
            result["issues"].extend(metadata_result["errors"])
            return result
        
        try:
            # Load metadata
            with open(os.path.join(self.database_path, "attribute_ids_by_pcat.json"), "r") as f:
                attribute_ids_by_pcat = json.load(f)
            
            # Check consistency between metadata and mapping files
            product_mapping_dir = os.path.join(self.database_path, "product_mapping")
            mapping_files = [f for f in os.listdir(product_mapping_dir) if f.endswith('.pkl')]
            mapping_categories = set(int(f.replace('.pkl', '')) for f in mapping_files)
            metadata_categories = set(int(k) for k in attribute_ids_by_pcat.keys())
            
            result["statistics"]["metadata_categories"] = len(metadata_categories)
            result["statistics"]["mapping_categories"] = len(mapping_categories)
            
            missing_mappings = metadata_categories - mapping_categories
            extra_mappings = mapping_categories - metadata_categories
            
            if missing_mappings:
                result["issues"].append(f"Categories in metadata but missing mapping files: {sorted(list(missing_mappings))[:10]}")
            
            if extra_mappings:
                result["issues"].append(f"Mapping files exist but not in metadata: {sorted(list(extra_mappings))[:10]}")
            
            if not result["issues"]:
                result["validation_status"] = "PASSED"
            else:
                result["validation_status"] = "ISSUES_FOUND"
                
        except Exception as e:
            result["validation_status"] = "FAILED"
            result["issues"].append(f"Validation failed: {str(e)}")
        
        return result


def debug_specific_error(database_path: str, category_id: int, product_id: Optional[int] = None) -> None:
    """
    Debug a specific inference error.
    
    Args:
        database_path: Path to the database directory
        category_id: The category ID causing the error
        product_id: Optional product ID for context
    """
    debugger = InferenceDebugger(database_path)
    
    print(f"\n=== Debugging Category {category_id} Error ===")
    if product_id:
        print(f"Product ID: {product_id}")
    print(f"Database: {database_path}")
    
    # Load and validate metadata
    print("\n--- Loading Metadata ---")
    metadata_result = debugger.load_metadata()
    
    for file_name, status in metadata_result["files_status"].items():
        print(f"{file_name}: {status}")
    
    if metadata_result["errors"]:
        print("\nERRORS:")
        for error in metadata_result["errors"]:
            print(f"  - {error}")
    
    if metadata_result["warnings"]:
        print("\nWARNINGS:")
        for warning in metadata_result["warnings"]:
            print(f"  - {warning}")
    
    # Investigate specific category
    print(f"\n--- Investigating Category {category_id} ---")
    investigation = debugger.investigate_category_error(category_id, product_id)
    
    for key, value in investigation["investigation"].items():
        print(f"{key}: {value}")
    
    if investigation["recommendations"]:
        print("\nRECOMMENDATIONS:")
        for rec in investigation["recommendations"]:
            print(f"  - {rec}")
    
    # Validate overall consistency
    print("\n--- Database Consistency Check ---")
    consistency = debugger.validate_database_consistency()
    print(f"Status: {consistency['validation_status']}")
    
    if consistency["statistics"]:
        print("Statistics:")
        for key, value in consistency["statistics"].items():
            print(f"  {key}: {value}")
    
    if consistency["issues"]:
        print("Issues:")
        for issue in consistency["issues"]:
            print(f"  - {issue}")


if __name__ == "__main__":
    # Example usage for debugging the '997' error
    import sys
    
    if len(sys.argv) < 3:
        print("Usage: python debug_inference.py <database_path> <category_id> [product_id]")
        sys.exit(1)
    
    db_path = sys.argv[1]
    cat_id = int(sys.argv[2])
    prod_id = int(sys.argv[3]) if len(sys.argv) > 3 else None
    
    debug_specific_error(db_path, cat_id, prod_id)
