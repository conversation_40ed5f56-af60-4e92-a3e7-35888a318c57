"""
SQL queries for inference metadata generation during preprocessing.
"""

# Query to get distinct product category IDs from local product table
DISTINCT_PCAT_IDS_FOR_INFERENCE_QUERY = """
SELECT DISTINCT(product_category_id) 
FROM product
WHERE product_category_id IS NOT NULL
"""

# Query to count products by category ID for inference
COUNT_PRODUCTS_BY_PCAT_FOR_INFERENCE_QUERY = """
SELECT COUNT(product_id) as count
FROM product 
WHERE product_category_id = {pcat_id}
"""

# Query to get attribute IDs by product category for inference
ATTRIBUTE_IDS_BY_PCAT_FOR_INFERENCE_QUERY = """
SELECT product_category_id, attribute_id, pcat_attribute_order 
FROM {attributes_by_category_table} 
ORDER BY product_category_id, pcat_attribute_order
"""

# Query to get attribute sizes from combined count table
ATTRIBUTE_SIZES_FOR_INFERENCE_QUERY = """
SELECT attribute_id, term_count
FROM combined_count
ORDER BY attribute_id
"""

# Query to get product category IDs for validation
PRODUCT_CATEGORY_IDS_FOR_VALIDATION_QUERY = """
SELECT product_category_id
FROM product
ORDER BY product_id
"""
